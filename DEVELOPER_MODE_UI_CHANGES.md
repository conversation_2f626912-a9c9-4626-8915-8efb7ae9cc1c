# 开发者模式UI隐藏功能实现

## 概述
根据需求，在非开发者模式下隐藏以下UI元素：
1. 模式选择器上的设置按钮
2. 插件市场按钮  
3. Profile选择的编辑按钮
4. Codebase按钮

## 修改的文件

### 1. ChatTextArea.tsx
- **位置**: `webview-ui/src/components/chat/ChatTextArea.tsx`
- **修改内容**:
  - 从`useExtensionState`中获取`developerMode`状态
  - 修改`getApiConfigOptions`函数，在非开发者模式下不显示编辑按钮（"settingsButtonClicked"选项）
  - 修改`renderNonEditModeControls`函数，在非开发者模式下隐藏`IndexingStatusBadge`组件（codebase按钮）

### 2. ModeSelector.tsx  
- **位置**: `webview-ui/src/components/chat/ModeSelector.tsx`
- **修改内容**:
  - 从`useExtensionState`中获取`developerMode`状态
  - 在模式选择器弹窗中，使用条件渲染`{developerMode && ...}`来隐藏设置按钮和插件市场按钮

### 3. ModeSelector.spec.tsx
- **位置**: `webview-ui/src/components/chat/__tests__/ModeSelector.spec.tsx`
- **修改内容**:
  - 更新测试mock，添加`developerMode: true`以确保测试正常运行

## 实现逻辑

### 开发者模式状态获取
开发者模式状态通过`ExtensionStateContext`提供，默认值为`false`。

### UI元素隐藏策略
- **设置按钮和插件市场按钮**: 在`ModeSelector`组件中使用条件渲染`{developerMode && <按钮组件>}`
- **编辑按钮**: 在构建API配置选项时，只有在开发者模式下才添加编辑选项
- **Codebase按钮**: 在渲染控制按钮时使用条件渲染`{developerMode && <IndexingStatusBadge />}`

## 功能保持
- 所有功能逻辑保持不变，只是在UI层面进行隐藏
- 开发者模式下所有按钮正常显示和工作
- 非开发者模式下相关功能仍然可以通过其他方式访问（如果有的话）

## 测试
- 更新了相关测试文件以确保兼容性
- 测试默认使用开发者模式以保证现有测试通过

## 使用方法
用户可以通过VSCode设置中的`developerMode`选项来控制这些UI元素的显示：
- `true`: 显示所有按钮（开发者模式）
- `false`: 隐藏指定的UI元素（普通用户模式）
