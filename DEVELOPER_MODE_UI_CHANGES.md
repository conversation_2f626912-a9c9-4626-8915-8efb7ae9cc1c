# 开发者模式UI隐藏功能实现

## 概述
根据需求，在非开发者模式下隐藏以下UI元素：
1. 模式选择器上的设置按钮
2. 插件市场按钮
3. Profile选择的编辑按钮
4. Codebase按钮
5. 设置页面中的高级功能标签页

## 修改的文件

### 1. ChatTextArea.tsx
- **位置**: `webview-ui/src/components/chat/ChatTextArea.tsx`
- **修改内容**:
  - 从`useExtensionState`中获取`developerMode`状态
  - 修改`getApiConfigOptions`函数，在非开发者模式下不显示编辑按钮（"settingsButtonClicked"选项）
  - 修改`renderNonEditModeControls`函数，在非开发者模式下隐藏`IndexingStatusBadge`组件（codebase按钮）

### 2. ModeSelector.tsx  
- **位置**: `webview-ui/src/components/chat/ModeSelector.tsx`
- **修改内容**:
  - 从`useExtensionState`中获取`developerMode`状态
  - 在模式选择器弹窗中，使用条件渲染`{developerMode && ...}`来隐藏设置按钮和插件市场按钮

### 3. SettingsView.tsx
- **位置**: `webview-ui/src/components/settings/SettingsView.tsx`
- **修改内容**:
  - 从`useExtensionState`中获取`developerMode`状态
  - 修改`sections`数组的生成逻辑，在非开发者模式下隐藏特定设置页面标签
  - 隐藏的设置页面包括：`language`（语言）、`experimental`（实验性功能）、`terminal`（终端）、`prompts`（提示词）、`contextManagement`（上下文管理）、`browser`（浏览器）、`providers`（提供商）
  - 修改默认标签页逻辑：开发者模式下默认显示`providers`，非开发者模式下默认显示`autoApprove`
  - 添加标签页可用性检查，当开发者模式改变时自动切换到可用的标签页

### 4. ModeSelector.spec.tsx
- **位置**: `webview-ui/src/components/chat/__tests__/ModeSelector.spec.tsx`
- **修改内容**:
  - 更新测试mock，添加`developerMode: true`以确保测试正常运行

## 实现逻辑

### 开发者模式状态获取
开发者模式状态通过`ExtensionStateContext`提供，默认值为`false`。

### UI元素隐藏策略
- **设置按钮和插件市场按钮**: 在`ModeSelector`组件中使用条件渲染`{developerMode && <按钮组件>}`
- **编辑按钮**: 在构建API配置选项时，只有在开发者模式下才添加编辑选项
- **Codebase按钮**: 在渲染控制按钮时使用条件渲染`{developerMode && <IndexingStatusBadge />}`
- **设置页面标签页**: 在`SettingsView`组件中过滤sections数组，移除非开发者模式下不应显示的标签页

## 功能保持
- 所有功能逻辑保持不变，只是在UI层面进行隐藏
- 开发者模式下所有按钮正常显示和工作
- 非开发者模式下相关功能仍然可以通过其他方式访问（如果有的话）

## 测试
- 更新了相关测试文件以确保兼容性
- 测试默认使用开发者模式以保证现有测试通过

## 详细功能说明

### 隐藏的设置页面标签页
在非开发者模式下，以下设置页面标签页将被隐藏：

1. **Providers（提供商）**: API配置和模型提供商设置
2. **Language（语言）**: 界面语言设置
3. **Experimental（实验性功能）**: 实验性功能开关
4. **Terminal（终端）**: 终端相关配置
5. **Prompts（提示词）**: 自定义提示词设置
6. **Context Management（上下文管理）**: 上下文处理相关设置
7. **Browser（浏览器）**: 浏览器集成相关设置

### 保留的设置页面标签页
非开发者模式下仍然可见的标签页：

1. **Auto Approve（自动批准）**: 自动批准设置（默认标签页）
2. **Checkpoints（检查点）**: 检查点功能设置
3. **Notifications（通知）**: 通知相关设置
4. **Completion（补全）**: 代码补全相关设置
5. **About（关于）**: 版本信息和关于页面

## 使用方法
用户可以通过VSCode设置中的`developerMode`选项来控制这些UI元素的显示：
- `true`: 显示所有按钮和设置页面（开发者模式）
- `false`: 隐藏指定的UI元素和高级设置页面（普通用户模式）
